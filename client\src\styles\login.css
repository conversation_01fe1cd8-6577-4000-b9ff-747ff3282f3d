/* Login Page Styles */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--primary-green-light) 0%, var(--primary-green) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
}

.login-card {
  width: 100%;
  max-width: 450px;
  background: var(--white);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-heavy);
  overflow: hidden;
  animation: fadeIn 0.5s ease-out;
}

.login-header {
  text-align: center;
  padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-lg);
  background: linear-gradient(135deg, var(--primary-green-light), var(--primary-green));
  color: var(--white);
}

.login-header .logo {
  font-size: 48px;
  margin-bottom: var(--spacing-sm);
}

.login-header h1 {
  font-size: var(--font-size-xl);
  margin: 0;
  margin-bottom: var(--spacing-xs);
  font-weight: 700;
}

.login-header h2 {
  font-size: var(--font-size-lg);
  margin: 0;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
  opacity: 0.95;
}

.login-header p {
  margin: 0;
  opacity: 0.8;
  font-size: var(--font-size-sm);
}

.login-form {
  padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-lg);
}

.btn-full {
  width: 100%;
  padding: var(--spacing-md);
  font-size: var(--font-size-base);
  font-weight: 600;
  margin-top: var(--spacing-sm);
}

.btn-small {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-sm);
}

.error-message {
  background-color: #ffebee;
  color: var(--error);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-medium);
  margin-bottom: var(--spacing-md);
  border: 1px solid #ffcdd2;
  font-size: var(--font-size-sm);
}

.login-footer {
  padding: var(--spacing-lg);
  text-align: center;
  background-color: var(--light-gray);
  border-top: 1px solid var(--medium-gray);
}

.login-footer p {
  margin: 0;
  color: var(--text-light);
  font-size: var(--font-size-sm);
}

.link-button {
  background: none;
  border: none;
  color: var(--primary-green);
  cursor: pointer;
  font-weight: 600;
  text-decoration: underline;
  font-size: inherit;
}

.link-button:hover {
  color: var(--primary-green-dark);
}

/* Form Enhancements */
.form-group {
  position: relative;
}

.form-input:focus + .form-label,
.form-input:not(:placeholder-shown) + .form-label {
  transform: translateY(-20px);
  font-size: var(--font-size-sm);
  color: var(--primary-green);
}

/* Loading State */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Responsive Design */
@media (max-width: 480px) {
  .login-container {
    padding: var(--spacing-md);
  }
  
  .login-card {
    max-width: 100%;
  }
  
  .login-header {
    padding: var(--spacing-lg) var(--spacing-md) var(--spacing-md);
  }
  
  .login-form {
    padding: var(--spacing-lg) var(--spacing-md) var(--spacing-md);
  }
  
  .login-footer {
    padding: var(--spacing-md);
  }
}