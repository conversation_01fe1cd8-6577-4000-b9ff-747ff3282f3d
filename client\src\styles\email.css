/* Email List Styles */
.email-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  max-height: 600px;
  overflow-y: auto;
  padding-right: var(--spacing-xs);
}

.email-list::-webkit-scrollbar {
  width: 6px;
}

.email-list::-webkit-scrollbar-track {
  background: var(--light-gray);
  border-radius: var(--radius-small);
}

.email-list::-webkit-scrollbar-thumb {
  background: var(--medium-gray);
  border-radius: var(--radius-small);
}

.email-list::-webkit-scrollbar-thumb:hover {
  background: var(--dark-gray);
}

.email-item {
  padding: var(--spacing-md);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.email-item:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.email-item.selected {
  border-color: var(--primary-green);
  background-color: #f1f8e9;
}

.email-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-sm);
}

.email-category {
  display: flex;
  align-items: center;
}

.category-icon {
  font-size: 20px;
  margin-right: var(--spacing-sm);
}

.email-meta {
  text-align: right;
  flex-shrink: 0;
}

.email-from {
  font-weight: 600;
  color: var(--text-dark);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-xs);
}

.email-date {
  font-size: var(--font-size-sm);
  color: var(--text-light);
}

.email-subject {
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-base);
  line-height: 1.4;
}

.email-snippet {
  color: var(--text-light);
  font-size: var(--font-size-sm);
  line-height: 1.5;
  margin-bottom: var(--spacing-sm);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.email-tags {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

.tag {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-small);
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tag-urgent {
  background-color: var(--error);
  color: var(--white);
}

.tag-reminder {
  background-color: var(--warning);
  color: var(--white);
}

.tag-cancelled {
  background-color: var(--dark-gray);
  color: var(--white);
}

/* Empty State */
.empty-state {
  padding: var(--spacing-xl);
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: var(--spacing-md);
  opacity: 0.5;
}

.empty-state h3 {
  color: var(--text-dark);
  margin-bottom: var(--spacing-sm);
}

.empty-state p {
  color: var(--text-light);
  margin: 0;
}

/* Priority Indicators */
.email-item.priority-high {
  border-left: 4px solid var(--error);
}

.email-item.priority-medium {
  border-left: 4px solid var(--warning);
}

.email-item.priority-low {
  border-left: 4px solid var(--info);
}

/* Responsive Design */
@media (max-width: 768px) {
  .email-header {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
  
  .email-meta {
    text-align: left;
    width: 100%;
  }
  
  .email-from,
  .email-date {
    display: inline;
    margin-right: var(--spacing-sm);
  }
  
  .email-date::before {
    content: "• ";
    color: var(--text-light);
  }
}