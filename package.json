{"name": "sprout", "version": "1.0.0", "description": "Family Activity Monitor", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "nodemon server/index.js", "client": "cd client && npm start", "build": "cd client && npm run build", "start": "node server/index.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "googleapis": "^126.0.1", "nodemailer": "^6.9.7"}, "devDependencies": {"nodemon": "^3.0.1", "concurrently": "^8.2.2"}}