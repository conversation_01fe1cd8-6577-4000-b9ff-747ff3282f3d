import React from 'react';
import '../styles/theme.css';

const LandingPage = ({ onShowLogin }) => {
  const scrollToFeatures = (e) => {
    e.preventDefault();
    e.stopPropagation();
    const featuresSection = document.getElementById('features-section');
    if (featuresSection) {
      featuresSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  return (
    <div className="landing-page">
      <div className="hero-section">
        <div className="hero-content">
          <div className="logo-section">
            <div className="logo">🌱</div>
            <h1>Sprout</h1>
            <p className="tagline">Family Activity Monitor</p>
          </div>
          
          <div className="hero-text">
            <h2>Never miss another school event or deadline</h2>
            <p>AI-powered email processing that keeps your family organized and informed about all activities, events, and important communications.</p>
          </div>
          
          <div className="cta-buttons">
            <button className="btn btn-primary btn-large" onClick={onShowLogin}>
              Get Started
            </button>
            <button
              type="button"
              className="btn btn-secondary btn-large"
              onClick={scrollToFeatures}
              aria-label="Scroll to features section"
            >
              Learn More
            </button>
          </div>
        </div>
      </div>
      
      <div className="features-section" id="features-section">
        <div className="container">
          <h2 className="text-center mb-lg">How Sprout Helps Your Family</h2>
          <div className="features-grid">
            <div className="feature-card card">
              <div className="feature-icon">📧</div>
              <h3>Smart Email Processing</h3>
              <p>Forward emails to Sprout and our AI automatically categorizes school events, homework reminders, and important communications.</p>
            </div>
            
            <div className="feature-card card">
              <div className="feature-icon">📅</div>
              <h3>Automatic Reminders</h3>
              <p>Never forget permission slips, picture day, or parent-teacher conferences. Get timely notifications across all your devices.</p>
            </div>
            
            <div className="feature-card card">
              <div className="feature-icon">👨‍👩‍👧‍👦</div>
              <h3>Family Sharing</h3>
              <p>Keep both parents, grandparents, and caregivers in the loop with shared family calendars and notifications.</p>
            </div>
            
            <div className="feature-card card">
              <div className="feature-icon">🎯</div>
              <h3>Priority Sorting</h3>
              <p>AI identifies urgent items like deadline reminders and medical forms, ensuring nothing important gets missed.</p>
            </div>
            
            <div className="feature-card card">
              <div className="feature-icon">📱</div>
              <h3>Multi-Platform Access</h3>
              <p>Access your family's activity dashboard from web, mobile, or receive notifications via SMS and email.</p>
            </div>
            
            <div className="feature-card card">
              <div className="feature-icon">🧠</div>
              <h3>Learning AI</h3>
              <p>The more you use Sprout, the better it gets at understanding your family's unique needs and preferences.</p>
            </div>
          </div>
        </div>
      </div>
      
      <div className="stats-section">
        <div className="container">
          <div className="stats-grid">
            <div className="stat-item">
              <div className="stat-number">98%</div>
              <div className="stat-label">Accuracy Rate</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">2.5k+</div>
              <div className="stat-label">Families Served</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">15min</div>
              <div className="stat-label">Weekly Time Saved</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">Zero</div>
              <div className="stat-label">Missed Events</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LandingPage;