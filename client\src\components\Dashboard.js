import React, { useState, useEffect } from 'react';
import EmailList from './EmailList';
import '../styles/theme.css';

const Dashboard = ({ user, onLogout }) => {
  const [messages, setMessages] = useState([]);
  const [selectedMessage, setSelectedMessage] = useState(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // Mock data for demonstration
  const mockActivities = [
    {
      id: 1,
      title: 'Picture Day - Lincoln Elementary',
      category: 'school_event',
      priority: 'medium',
      date: '2024-02-15',
      child: 'Emma',
      status: 'upcoming',
      description: 'School picture day. Remember to dress <PERSON> in her blue dress.'
    },
    {
      id: 2,
      title: 'Soccer Practice Cancelled',
      category: 'sports',
      priority: 'high',
      date: '2024-02-12',
      child: 'Jake',
      status: 'urgent',
      description: 'Due to weather conditions, soccer practice is cancelled today.'
    },
    {
      id: 3,
      title: 'Parent-Teacher Conference',
      category: 'school_event',
      priority: 'high',
      date: '2024-02-20',
      child: 'Emma',
      status: 'scheduled',
      description: 'Meeting with Mrs. <PERSON> to discuss <PERSON>\'s progress.'
    }
  ];

  const mockReminders = [
    { id: 1, text: 'Permission slip for field trip due tomorrow', priority: 'high' },
    { id: 2, text: 'Bring snacks for Emma\'s class party on Friday', priority: 'medium' },
    { id: 3, text: 'Jake\'s soccer uniform needs washing', priority: 'low' }
  ];

  const fetchMessages = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/email/messages', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setMessages(data);
      }
    } catch (error) {
      console.error('Error fetching messages:', error);
      // Use mock data for demo
      setMessages([
        {
          id: '1',
          from: 'Lincoln Elementary <<EMAIL>>',
          subject: 'Picture Day Reminder - February 15th',
          snippet: 'Don\'t forget! Picture day is coming up on February 15th. Please ensure your child is dressed appropriately...',
          date: '2024-02-10'
        },
        {
          id: '2', 
          from: 'Youth Soccer League <<EMAIL>>',
          subject: 'URGENT: Practice Cancelled Today',
          snippet: 'Due to severe weather conditions, all soccer practices scheduled for today have been cancelled...',
          date: '2024-02-12'
        }
      ]);
    }
    setLoading(false);
  };

  const fetchMessage = async (messageId) => {
    try {
      const response = await fetch(`/api/email/messages/${messageId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setSelectedMessage(data);
      }
    } catch (error) {
      console.error('Error fetching message:', error);
      // Mock selected message
      const mockMessage = {
        id: messageId,
        from: 'Lincoln Elementary <<EMAIL>>',
        subject: 'Picture Day Reminder - February 15th',
        date: '2024-02-10',
        body: `Dear Parents,\n\nThis is a friendly reminder that Picture Day is scheduled for Thursday, February 15th.\n\nPlease ensure your child:\n- Arrives on time\n- Is dressed appropriately\n- Has their hair combed\n\nPhotos will be taken between 9:00 AM and 11:00 AM.\n\nThank you,\nLincoln Elementary Staff`
      };
      setSelectedMessage(mockMessage);
    }
  };

  useEffect(() => {
    fetchMessages();
  }, []);

  const renderOverview = () => (
    <div className="dashboard-overview">
      <div className="stats-cards">
        <div className="stat-card card">
          <div className="stat-icon">📧</div>
          <div className="stat-info">
            <div className="stat-number">12</div>
            <div className="stat-label">New Messages</div>
          </div>
        </div>
        <div className="stat-card card">
          <div className="stat-icon">⏰</div>
          <div className="stat-info">
            <div className="stat-number">5</div>
            <div className="stat-label">Upcoming Events</div>
          </div>
        </div>
        <div className="stat-card card">
          <div className="stat-icon">🚨</div>
          <div className="stat-info">
            <div className="stat-number">2</div>
            <div className="stat-label">Urgent Items</div>
          </div>
        </div>
        <div className="stat-card card">
          <div className="stat-icon">👨👩👧👦</div>
          <div className="stat-info">
            <div className="stat-number">2</div>
            <div className="stat-label">Children</div>
          </div>
        </div>
      </div>

      <div className="dashboard-grid">
        <div className="dashboard-section">
          <h3>Recent Activities</h3>
          <div className="activity-list">
            {mockActivities.map(activity => (
              <div key={activity.id} className={`activity-item card priority-${activity.priority}`}>
                <div className="activity-header">
                  <h4>{activity.title}</h4>
                  <span className={`badge badge-${activity.priority === 'high' ? 'warning' : activity.priority === 'medium' ? 'info' : 'success'}`}>
                    {activity.priority}
                  </span>
                </div>
                <div className="activity-meta">
                  <span className="activity-child">👤 {activity.child}</span>
                  <span className="activity-date">📅 {activity.date}</span>
                </div>
                <p className="activity-description">{activity.description}</p>
              </div>
            ))}
          </div>
        </div>

        <div className="dashboard-section">
          <h3>Quick Reminders</h3>
          <div className="reminder-list">
            {mockReminders.map(reminder => (
              <div key={reminder.id} className={`reminder-item priority-${reminder.priority}`}>
                <div className="reminder-text">{reminder.text}</div>
                <button className="btn btn-small btn-accent">Mark Done</button>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="dashboard">
      <header className="dashboard-header">
        <div className="header-left">
          <div className="logo">🌱</div>
          <div>
            <h1>Sprout</h1>
            <p className="header-subtitle">Family Activity Monitor</p>
          </div>
        </div>
        <div className="header-right">
          <div className="user-info">
            <span className="user-name">Welcome, {user?.name || user?.email}</span>
            <div className="user-avatar">👤</div>
          </div>
          <button onClick={onLogout} className="btn btn-secondary">
            Logout
          </button>
        </div>
      </header>

      <nav className="dashboard-nav">
        <button 
          className={`nav-tab ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          📊 Overview
        </button>
        <button 
          className={`nav-tab ${activeTab === 'messages' ? 'active' : ''}`}
          onClick={() => setActiveTab('messages')}
        >
          📧 Messages
        </button>
        <button 
          className={`nav-tab ${activeTab === 'calendar' ? 'active' : ''}`}
          onClick={() => setActiveTab('calendar')}
        >
          📅 Calendar
        </button>
        <button 
          className={`nav-tab ${activeTab === 'family' ? 'active' : ''}`}
          onClick={() => setActiveTab('family')}
        >
          👨👩👧👦 Family
        </button>
      </nav>

      <main className="dashboard-content">
        {activeTab === 'overview' && renderOverview()}
        
        {activeTab === 'messages' && (
          <div className="messages-view">
            <div className="messages-header flex-between mb-lg">
              <h2>Recent Messages</h2>
              <button 
                onClick={fetchMessages} 
                disabled={loading}
                className="btn btn-primary"
              >
                {loading ? 'Loading...' : '🔄 Refresh'}
              </button>
            </div>
            
            <div className="messages-layout">
              <div className="messages-list">
                <EmailList 
                  messages={messages} 
                  onSelectMessage={fetchMessage}
                  selectedMessageId={selectedMessage?.id}
                />
              </div>

              {selectedMessage && (
                <div className="message-detail card">
                  <h3>Message Details</h3>
                  <div className="message-meta">
                    <div className="meta-item">
                      <strong>From:</strong> {selectedMessage.from}
                    </div>
                    <div className="meta-item">
                      <strong>Subject:</strong> {selectedMessage.subject}
                    </div>
                    <div className="meta-item">
                      <strong>Date:</strong> {selectedMessage.date}
                    </div>
                  </div>
                  <div className="message-content">
                    <strong>Content:</strong>
                    <div className="message-body">
                      {selectedMessage.body || selectedMessage.snippet}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
        
        {activeTab === 'calendar' && (
          <div className="calendar-placeholder card text-center">
            <div className="placeholder-icon">📅</div>
            <h3>Calendar View</h3>
            <p className="text-muted">Calendar integration coming soon. View all your family's events and deadlines in one place.</p>
          </div>
        )}
        
        {activeTab === 'family' && (
          <div className="family-placeholder card text-center">
            <div className="placeholder-icon">👨👩👧👦</div>
            <h3>Family Management</h3>
            <p className="text-muted">Manage family members, children profiles, and sharing settings.</p>
            <div className="family-members mt-lg">
              <div className="member-card card">
                <div className="member-avatar">👤</div>
                <div className="member-info">
                  <h4>Emma (Age 8)</h4>
                  <p>Lincoln Elementary - 3rd Grade</p>
                </div>
              </div>
              <div className="member-card card">
                <div className="member-avatar">👤</div>
                <div className="member-info">
                  <h4>Jake (Age 12)</h4>
                  <p>Roosevelt Middle School - 7th Grade</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default Dashboard;