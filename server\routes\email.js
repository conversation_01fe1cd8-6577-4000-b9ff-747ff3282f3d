const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const GmailService = require('../services/gmail');
const router = express.Router();

const gmailService = new GmailService();

router.get('/messages', authenticateToken, async (req, res) => {
  try {
    const messages = await gmailService.getMessages();
    res.json(messages);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch messages' });
  }
});

router.get('/messages/:id', authenticateToken, async (req, res) => {
  try {
    const message = await gmailService.getMessage(req.params.id);
    if (!message) {
      return res.status(404).json({ error: 'Message not found' });
    }
    res.json(message);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch message' });
  }
});

module.exports = router;