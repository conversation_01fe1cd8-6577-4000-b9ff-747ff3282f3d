import React from 'react';
import '../styles/theme.css';

const EmailList = ({ messages, onSelectMessage, selectedMessageId }) => {
  if (!messages || messages.length === 0) {
    return (
      <div className="empty-state card text-center">
        <div className="empty-icon">📧</div>
        <h3>No messages found</h3>
        <p className="text-muted">Your processed emails will appear here</p>
      </div>
    );
  }

  const getPriorityClass = (subject) => {
    if (subject.toLowerCase().includes('urgent') || subject.toLowerCase().includes('cancelled')) {
      return 'priority-high';
    }
    if (subject.toLowerCase().includes('reminder') || subject.toLowerCase().includes('due')) {
      return 'priority-medium';
    }
    return 'priority-low';
  };

  const getCategoryIcon = (subject) => {
    if (subject.toLowerCase().includes('school') || subject.toLowerCase().includes('picture')) {
      return '🏫';
    }
    if (subject.toLowerCase().includes('soccer') || subject.toLowerCase().includes('sports')) {
      return '⚽';
    }
    if (subject.toLowerCase().includes('medical') || subject.toLowerCase().includes('health')) {
      return '🏥';
    }
    return '📧';
  };

  return (
    <div className="email-list">
      {messages.map((message) => (
        <div 
          key={message.id}
          onClick={() => onSelectMessage(message.id)}
          className={`email-item card ${getPriorityClass(message.subject)} ${
            selectedMessageId === message.id ? 'selected' : ''
          }`}
        >
          <div className="email-header">
            <div className="email-category">
              <span className="category-icon">{getCategoryIcon(message.subject)}</span>
            </div>
            <div className="email-meta">
              <div className="email-from">{message.from}</div>
              <div className="email-date">{message.date}</div>
            </div>
          </div>
          
          <div className="email-subject">{message.subject}</div>
          
          <div className="email-snippet">
            {message.snippet}
          </div>
          
          <div className="email-tags">
            {message.subject.toLowerCase().includes('urgent') && (
              <span className="tag tag-urgent">Urgent</span>
            )}
            {message.subject.toLowerCase().includes('reminder') && (
              <span className="tag tag-reminder">Reminder</span>
            )}
            {message.subject.toLowerCase().includes('cancelled') && (
              <span className="tag tag-cancelled">Cancelled</span>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default EmailList;