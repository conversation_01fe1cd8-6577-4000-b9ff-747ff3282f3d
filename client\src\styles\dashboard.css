/* Dashboard Styles */
.dashboard {
  min-height: 100vh;
  background-color: var(--light-gray);
}

.dashboard-header {
  background: var(--white);
  padding: var(--spacing-md) var(--spacing-lg);
  box-shadow: var(--shadow-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--medium-gray);
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.header-left .logo {
  font-size: 32px;
}

.header-left h1 {
  margin: 0;
  color: var(--primary-green);
  font-size: var(--font-size-xl);
  font-weight: 700;
}

.header-subtitle {
  margin: 0;
  color: var(--text-light);
  font-size: var(--font-size-sm);
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.user-name {
  color: var(--text-dark);
  font-weight: 500;
}

.user-avatar {
  width: 32px;
  height: 32px;
  background: var(--primary-green-light);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.dashboard-nav {
  background: var(--white);
  padding: 0 var(--spacing-lg);
  display: flex;
  gap: var(--spacing-sm);
  border-bottom: 1px solid var(--medium-gray);
  overflow-x: auto;
}

.nav-tab {
  background: none;
  border: none;
  padding: var(--spacing-md) var(--spacing-lg);
  cursor: pointer;
  font-size: var(--font-size-base);
  color: var(--text-light);
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.nav-tab:hover {
  color: var(--primary-green);
  background-color: var(--light-gray);
}

.nav-tab.active {
  color: var(--primary-green);
  border-bottom-color: var(--primary-green);
  font-weight: 600;
}

.dashboard-content {
  padding: var(--spacing-lg);
  max-width: 1400px;
  margin: 0 auto;
}

/* Overview Section */
.dashboard-overview {
  animation: fadeIn 0.5s ease-in;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.stat-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  background: var(--primary-green-light);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-number {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary-green-dark);
  margin: 0;
}

.stat-label {
  color: var(--text-light);
  font-size: var(--font-size-sm);
  margin: 0;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-lg);
}

.dashboard-section h3 {
  color: var(--primary-green-dark);
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-lg);
}

/* Activity List */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.activity-item {
  padding: var(--spacing-md);
  transition: transform 0.3s ease;
}

.activity-item:hover {
  transform: translateX(5px);
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-sm);
}

.activity-header h4 {
  margin: 0;
  color: var(--text-dark);
  font-size: var(--font-size-base);
}

.activity-meta {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--text-light);
}

.activity-description {
  margin: 0;
  color: var(--text-light);
  font-size: var(--font-size-sm);
  line-height: 1.5;
}

/* Reminder List */
.reminder-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.reminder-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background: var(--white);
  border-radius: var(--radius-medium);
  box-shadow: var(--shadow-light);
}

.reminder-text {
  flex: 1;
  font-size: var(--font-size-sm);
  color: var(--text-dark);
}

/* Messages View */
.messages-view {
  animation: fadeIn 0.5s ease-in;
}

.messages-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
}

.messages-list {
  max-height: 600px;
  overflow-y: auto;
}

.message-detail {
  max-height: 600px;
  overflow-y: auto;
}

.message-meta {
  margin-bottom: var(--spacing-lg);
}

.meta-item {
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-sm);
}

.message-content {
  margin-top: var(--spacing-lg);
}

.message-body {
  margin-top: var(--spacing-sm);
  padding: var(--spacing-md);
  background-color: var(--light-gray);
  border-radius: var(--radius-medium);
  white-space: pre-wrap;
  font-size: var(--font-size-sm);
  line-height: 1.6;
  border: 1px solid var(--medium-gray);
}

/* Placeholder Sections */
.calendar-placeholder,
.family-placeholder {
  padding: var(--spacing-xl);
  text-align: center;
  animation: fadeIn 0.5s ease-in;
}

.placeholder-icon {
  font-size: 64px;
  margin-bottom: var(--spacing-lg);
}

.family-members {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-md);
  max-width: 600px;
  margin: 0 auto;
}

.member-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  text-align: left;
}

.member-avatar {
  width: 48px;
  height: 48px;
  background: var(--accent-orange-light);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.member-info h4 {
  margin: 0;
  color: var(--text-dark);
  font-size: var(--font-size-base);
}

.member-info p {
  margin: 0;
  color: var(--text-light);
  font-size: var(--font-size-sm);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .messages-layout {
    grid-template-columns: 1fr;
  }
  
  .stats-cards {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
  
  .header-left,
  .header-right {
    justify-content: center;
  }
  
  .dashboard-content {
    padding: var(--spacing-md);
  }
  
  .stats-cards {
    grid-template-columns: 1fr;
  }
  
  .dashboard-nav {
    padding: 0 var(--spacing-md);
  }
  
  .nav-tab {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
  }
}