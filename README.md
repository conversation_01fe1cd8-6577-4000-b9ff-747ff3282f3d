# Sprout - Family Activity Monitor

A minimal Node.js Express service with React frontend for monitoring family activities through email processing.

## Features

- Basic authentication (register/login)
- Gmail integration for email processing
- React frontend dashboard
- JWT-based authentication

## Setup

1. Install dependencies:
```bash
npm install
cd client && npm install
```

2. Configure environment variables:
```bash
cp .env.example .env
# Edit .env with your Gmail API credentials
```

3. Gmail API Setup:
   - Go to Google Cloud Console
   - Enable Gmail API
   - Create OAuth 2.0 credentials
   - Add your credentials to .env file

4. Run the application:
```bash
npm run dev
```

## Project Structure

```
sprout/
├── server/
│   ├── index.js          # Express server
│   ├── routes/
│   │   ├── auth.js       # Authentication routes
│   │   └── email.js      # Email processing routes
│   ├── middleware/
│   │   └── auth.js       # JWT middleware
│   └── services/
│       └── gmail.js      # Gmail API service
├── client/
│   └── src/
│       ├── App.js        # Main React app
│       └── components/   # React components
└── specs/                # Project specifications
```

## API Endpoints

- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/email/messages` - Fetch Gmail messages
- `GET /api/email/messages/:id` - Get specific message

## Next Steps

- Add database integration (PostgreSQL/MongoDB)
- Implement AI processing pipeline
- Add notification system
- Enhance UI/UX
- Add family management features