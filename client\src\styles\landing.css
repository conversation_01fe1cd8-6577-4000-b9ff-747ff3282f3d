/* Landing Page Styles */
.landing-page {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--primary-green-light) 0%, var(--primary-green) 100%);
}

.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: var(--white);
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  padding: var(--spacing-xl);
}

.logo-section {
  margin-bottom: var(--spacing-xl);
}

.logo {
  font-size: 80px;
  margin-bottom: var(--spacing-sm);
  display: inline-block;
  animation: pulse 3s infinite;
}

.hero-content h1 {
  font-size: var(--font-size-xxl);
  font-weight: 700;
  margin: 0;
  margin-bottom: var(--spacing-xs);
}

.tagline {
  font-size: var(--font-size-lg);
  opacity: 0.9;
  margin: 0;
  margin-bottom: var(--spacing-xl);
}

.hero-text h2 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  line-height: 1.3;
}

.hero-text p {
  font-size: var(--font-size-lg);
  opacity: 0.9;
  margin-bottom: var(--spacing-xl);
  line-height: 1.6;
}

.cta-buttons {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
}

.btn-large {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
  font-weight: 600;
}

.features-section {
  background: var(--white);
  padding: 80px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.feature-card {
  text-align: center;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-icon {
  font-size: 48px;
  margin-bottom: var(--spacing-md);
}

.feature-card h3 {
  color: var(--primary-green-dark);
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-lg);
}

.feature-card p {
  color: var(--text-light);
  line-height: 1.6;
}

.stats-section {
  background: var(--primary-green-dark);
  color: var(--white);
  padding: 60px 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  text-align: center;
}

.stat-item {
  padding: var(--spacing-lg);
}

.stat-number {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: var(--spacing-sm);
  color: var(--accent-orange-light);
}

.stat-label {
  font-size: var(--font-size-lg);
  opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-content {
    padding: var(--spacing-lg);
  }
  
  .logo {
    font-size: 60px;
  }
  
  .hero-content h1 {
    font-size: 28px;
  }
  
  .hero-text h2 {
    font-size: 22px;
  }
  
  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .btn-large {
    width: 100%;
    max-width: 300px;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stat-number {
    font-size: 36px;
  }
}